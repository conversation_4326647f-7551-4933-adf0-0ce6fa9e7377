// This file is for manually overriding product size and category data.
// Is used for products that do not have listed sizes to be scraped, or have incorrect categories.

export const productOverrides = [
  { id: '206889', size: '180g' },
  { id: '196996', size: '300g' },
  { id: '137967', size: '420g' },
  { id: '125856', size: '450g' },
  { id: '189268', size: '1.13kg' },
  { id: '189150', size: '1.2kg' },
  { id: '190454', size: '2.1kg' },
  { id: '189078', size: '1.3kg' },
  { id: '189136', size: '1.2kg' },
  { id: '755237', size: '931g' },
  { id: '755304', size: '1.1kg' },
  { id: '755246', size: '1020g' },
  { id: '755245', size: '1.2kg' },
  { id: '112273', size: '865ml' },
  { id: '269514', size: '584ml' },
  { id: '269515', size: '584ml' },
  { id: '116518', size: '440ml' },
  { id: '151191', size: '570ml' },
  { id: '279904', size: '575ml' },
  { id: '146149', size: '1000ml' },
  { id: '791925', size: '525g' },
  { id: '774216', size: '525g' },
  { id: '784406', size: '525g' },
  { id: '791916', size: '525g' },
  { id: '306624', size: '185g' },
  { id: '156824', size: '180g' },
  { id: '9023', size: '375g' },
  { id: '266962', category: 'sweets-lollies' },
  { id: '171524', size: '230ml', category: 'baking' },
  { id: '170021', category: 'ice-blocks' },
  { id: '71164', category: 'sausages' },
  { id: '71174', category: 'sausages' },
  { id: '71168', category: 'sausages' },
  { id: '71165', category: 'sausages' },
  { id: '331560', category: 'specialty-bread' },
  { id: '679412', category: 'herbal-tea' },
  { id: '790129', category: 'herbal-tea' },
  { id: '267492', category: 'herbal-tea' },
  { id: '267485', category: 'herbal-tea' },
  { id: '413302', category: 'herbal-tea' },
  { id: '267488', category: 'herbal-tea' },
  { id: '760872', category: 'herbal-tea' },
  { id: '681177', category: 'herbal-tea' },
  { id: '95091', category: 'herbal-tea' },
  { id: '761093', category: 'black-tea' },
  { id: '721661', category: 'green-tea' },
  { id: '790129', category: 'herbal-tea' },
  { id: '267492', category: 'herbal-tea' },
  { id: '267485', category: 'herbal-tea' },
  { id: '721034', category: 'herbal-tea' },
  { id: '413302', category: 'herbal-tea' },
  { id: '267488', category: 'herbal-tea' },
  { id: '760872', category: 'herbal-tea' },
  { id: '681177', category: 'herbal-tea' },
  { id: '95091.', category: 'herbal-tea' },
  { id: '184090', category: 'herbal-tea' },
  { id: '761093', category: 'black-tea' },
  { id: '721661', category: 'green-tea' },
  { id: '690093', category: 'green-tea' },
  { id: '780922', category: 'sauces' },
  { id: '780921', category: 'sauces' },
  { id: '72618', category: 'black-tea' },
  { id: '6053', category: 'black-tea' },
  { id: '72617', category: 'black-tea' },
  { id: '168068', category: 'black-tea' },
  { id: '6052', category: 'black-tea' },
  { id: '761436', category: 'black-tea' },
];
