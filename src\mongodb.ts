// Used by index.ts for creating and accessing items stored in MongoDB

import * as dotenv from "dotenv";
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });

import { MongoClient, Db, Collection } from "mongodb";
import { logError, log, colour, validCategories } from "./utilities";
import { Product, UpsertResponse, ProductResponse } from "./typings";

let mongoClient: MongoClient;
let database: Db;
let collection: Collection<Product>;

export async function establishMongoDB() {
  // Get MongoDB connection string stored in .env
  const MONGODB_CONNECTION_STRING = process.env.MONGODB_CONNECTION_STRING;
  if (!MONGODB_CONNECTION_STRING) {
    throw Error(
      "MongoDB connection string MONGODB_CONNECTION_STRING not found in .env"
    );
  }

  // Get database and collection names from environment variables
  const dbName = process.env.MONGODB_DB_NAME || "woolworths_scraper";
  const collectionName = process.env.MONGODB_COLLECTION || "products";

  // Establish MongoDB Client, Database, Collection
  try {
    mongoClient = new MongoClient(MONGODB_CONNECTION_STRING);
    await mongoClient.connect();

    database = mongoClient.db(dbName);
    collection = database.collection<Product>(collectionName);

    // Create indexes for better performance
    await collection.createIndex({ id: 1 }, { unique: true });
    await collection.createIndex({ name: 1 });
    await collection.createIndex({ category: 1 });
    await collection.createIndex({ lastUpdated: 1 });

    log(colour.yellow, `Connected to MongoDB database: ${dbName}, collection: ${collectionName}`);
  } catch (error) {
    throw Error(error + "\n\nInvalid MongoDB connection - check for valid connection string");
  }
}

// upsertProductToMongoDB()
// ------------------------
// Inserts or updates a product object to MongoDB,
//  returns an UpsertResponse based on if and how the Product was updated

export async function upsertProductToMongoDB(
  scrapedProduct: Product
): Promise<UpsertResponse> {
  try {
    // Check MongoDB for any existing item using the product ID
    const existingProduct = await collection.findOne({ id: scrapedProduct.id });

    // If an existing item was found in MongoDB, check for update values before uploading
    if (existingProduct) {
      const response = buildUpdatedProduct(scrapedProduct, existingProduct);

      // Send updated product to MongoDB
      await collection.replaceOne({ id: scrapedProduct.id }, response.product);
      return response.upsertType;
    } else {
      // If no existing product was found, create a new product
      await collection.insertOne(scrapedProduct);

      console.log(
        `  New Product: ${scrapedProduct.name.slice(0, 47).padEnd(47)}` +
        ` | $ ${scrapedProduct.currentPrice}`
      );

      return UpsertResponse.NewProduct;
    }
  } catch (e: any) {
    // Handle duplicate key errors (in case of race conditions)
    if (e.code === 11000) {
      logError(`Duplicate product ID found for product ${scrapedProduct.name}`);
      return UpsertResponse.Failed;
    }
    logError(e);
    return UpsertResponse.Failed;
  }
}

// buildUpdatedProduct()
// ---------------------
// This takes a freshly scraped product and compares it with a found database product.
// It returns an updated product with data from both product versions

function buildUpdatedProduct(
  scrapedProduct: Product,
  dbProduct: Product
): ProductResponse {
  // Date objects pulled from CosmosDB need to re-parsed as strings in format yyyy-mm-dd
  let dbDay = dbProduct.lastUpdated.toString();
  dbDay = dbDay.slice(0, 10);
  let scrapedDay = scrapedProduct.lastUpdated.toISOString().slice(0, 10);

  // Measure the price difference between the new scraped product and the old db product
  const priceDifference = Math.abs(
    dbProduct.currentPrice - scrapedProduct.currentPrice
  );

  // If price has changed by more than $0.05, and not on the same day
  if (priceDifference > 0.05 && dbDay != scrapedDay) {
    // Push scraped priceHistory into existing priceHistory array
    dbProduct.priceHistory.push(scrapedProduct.priceHistory[0]);

    // Set the scrapedProduct to use the updated priceHistory
    scrapedProduct.priceHistory = dbProduct.priceHistory;

    // Return completed Product ready for uploading
    logPriceChange(dbProduct, scrapedProduct.currentPrice);
    return {
      upsertType: UpsertResponse.PriceChanged,
      product: scrapedProduct,
    };
  }

  // If any db categories are not included within the list of valid ones, update to scraped ones
  else if (
    !dbProduct.category.every((category) => {
      const isValid = validCategories.includes(category);
      return isValid;
    }) ||
    dbProduct.category === null
  ) {
    console.log(
      `  Categories Changed: ${scrapedProduct.name
        .padEnd(40)
        .substring(0, 40)}` +
      ` - ${dbProduct.category.join(" ")} > ${scrapedProduct.category.join(
        " "
      )}`
    );

    // Update everything but priceHistory and lastUpdated
    scrapedProduct.priceHistory = dbProduct.priceHistory;
    scrapedProduct.lastUpdated = dbProduct.lastUpdated;

    // Return completed Product ready for uploading
    return {
      upsertType: UpsertResponse.InfoChanged,
      product: scrapedProduct,
    };
  }

  // Update other info
  else if (
    dbProduct.sourceSite !== scrapedProduct.sourceSite ||
    dbProduct.category.join(" ") !== scrapedProduct.category.join(" ") ||
    dbProduct.size !== scrapedProduct.size ||
    dbProduct.unitPrice !== scrapedProduct.unitPrice ||
    dbProduct.unitName !== scrapedProduct.unitName ||
    dbProduct.originalUnitQuantity !== scrapedProduct.originalUnitQuantity
  ) {
    // Update everything but priceHistory and lastUpdated
    scrapedProduct.priceHistory = dbProduct.priceHistory;
    scrapedProduct.lastUpdated = dbProduct.lastUpdated;

    // Return completed Product ready for uploading
    return {
      upsertType: UpsertResponse.InfoChanged,
      product: scrapedProduct,
    };
  } else {
    // Nothing has changed, only update lastChecked
    dbProduct.lastChecked = scrapedProduct.lastChecked;
    return {
      upsertType: UpsertResponse.AlreadyUpToDate,
      product: dbProduct,
    };
  }
}

// logPriceChange()
// ----------------
// Log a per product price change message,
//  coloured green for price reduction, red for price increase

export function logPriceChange(product: Product, newPrice: number) {
  const priceIncreased = newPrice > product.currentPrice;
  log(
    priceIncreased ? colour.red : colour.green,
    "  Price " +
    (priceIncreased ? "Up   : " : "Down : ") +
    product.name.slice(0, 47).padEnd(47) +
    " | $" +
    product.currentPrice.toString().padStart(4) +
    " > $" +
    newPrice
  );
}

// customQuery()
// -------------
// Function for running custom DB queries - used primarily for debugging

export async function customQuery(): Promise<void> {
  const batchSize = 30;
  const secondsDelayBetweenBatches = 5;

  log(colour.yellow, "Custom Query - Processing all products");

  let batchCount = 0;
  const maxBatchCount = 900;
  let continueFetching = true;
  let skip = 0;

  while (continueFetching) {
    await delayedBatchFetch();
  }

  console.log("Custom Query Complete");
  return;

  function delayedBatchFetch() {
    return new Promise<void>((resolve) =>
      setTimeout(async () => {
        console.log(
          "Batch " +
          batchCount +
          " - Items [" +
          skip +
          " - " +
          (skip + batchSize) +
          "]"
        );

        const products = await collection.find({})
          .skip(skip)
          .limit(batchSize)
          .toArray();

        if (products.length === 0) {
          continueFetching = false;
          resolve();
          return;
        }

        products.forEach(async (p) => {
          let oldDatedPrice = 0;
          let requiresUpdate = false;

          p.priceHistory.forEach((datedPrice) => {
            let newDatedPrice = datedPrice.price;
            if (Math.abs(oldDatedPrice - newDatedPrice) < 0.04) {
              console.log(p.name);
              console.log(
                " - Tiny price difference detected on " +
                datedPrice.date.toString() +
                " - " +
                oldDatedPrice +
                " - " +
                newDatedPrice
              );
              datedPrice.price = 0;
              requiresUpdate = true;
            }
            oldDatedPrice = newDatedPrice;
          });

          if (requiresUpdate) {
            let updatedPriceHistory = p.priceHistory.filter((datedPrice) => {
              if (datedPrice.price > 0) return true;
              else return false;
            });

            console.log(
              " - Old price history length: " +
              p.priceHistory.length +
              " - new length: " +
              updatedPriceHistory.length
            );

            p.priceHistory = updatedPriceHistory;

            const uploadRes = await collection.replaceOne({ id: p.id }, p);
            console.log(
              " - Uploaded updated product with acknowledged: " +
              uploadRes.acknowledged
            );
          }
        });

        skip += batchSize;
        if (batchCount++ === maxBatchCount) continueFetching = false;

        resolve();
      }, secondsDelayBetweenBatches * 1000)
    );
  }
}

// closeMongoDB()
// --------------
// Closes the MongoDB connection

export async function closeMongoDB(): Promise<void> {
  if (mongoClient) {
    await mongoClient.close();
    log(colour.yellow, "MongoDB connection closed");
  }
}
